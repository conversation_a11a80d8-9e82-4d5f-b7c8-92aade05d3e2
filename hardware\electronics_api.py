import asyncio
import subprocess
import logging
import os
from typing import Optional, <PERSON><PERSON>
from config import device_config
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
BOARDCTL_PATH = os.path.join(SCRIPT_DIR, "boardctl.py")
PYTHON_ALIAS = os.getenv("PYTHON_ALIAS", "py")  # Get python alias from .env file, default to "py"

async def execute_command(cmd: list, command_type: str) -> tuple[str, str, str]:
    """
    Execute command and return its output
    
    Args:
        cmd: List of parts of the command to execute
        command_type: Type of command (unlock, check_door, lock)
        
    Returns:
        tuple[str, str, str]: (stdout, stderr, error_code)
        error_code is None on success, otherwise contains error code
    """
    print(f"cmd: {cmd}, command_type: {command_type}")
    print("anooooooo 1")
    def run_command():
        try:
            proc = subprocess.Popen(
                [str(x) for x in cmd],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=SCRIPT_DIR
            )
            print("anooooo 5")
            stdout, stderr = proc.communicate(timeout=5) 
            print("anooooo 6")
            return stdout, stderr, None
        except subprocess.TimeoutExpired:
            logger.error(f"Command timeout: {' '.join(map(str, cmd))}")
            proc.kill()
            return b"Transmission Timeout Exception\n-12", b"", "-12"
        except Exception as e:
            logger.error(f"Error executing command: {e}")
            return str(e).encode(), b"", "-22"

    print("anooooo 2")
    loop = asyncio.get_running_loop()
    stdout, stderr, error_from_timeout = await loop.run_in_executor(None, run_command)
    print("anooooo 7")
    
    stdout_text = stdout.decode().strip()
    stderr_text = stderr.decode().strip()
    
    logger.info(f"Command output - stdout:\n{stdout_text}")
    if stderr_text:
        logger.info(f"Command output - stderr:\n{stderr_text}")

    if error_from_timeout:
        print(f"anooooo 3, error_code: {error_code}")
        return stdout_text, stderr_text, error_from_timeout

    lines = stdout_text.split('\n')
    last_line = lines[-1].strip() if lines else ""
    
    error_code = None
    if "Transmission Timeout Exception" in stdout_text:
        error_code = "-12"
    elif last_line.startswith('-'):
        error_code = last_line
    elif not (last_line == "1" or "Script finished successfuly" in stdout_text):
        error_code = "-22" 

    print(f"anooooo 3, error_code: {error_code}")
    return stdout_text, stderr_text, error_code

async def send_command(locker_id: str, command: str, max_retries: int = 2, is_tempered: bool = True) -> str:
    """
    Command to boardctl.py
    
    Args:
        locker_id: ID  (1-206)
        command: (unlock, check_door, lock, ...)
        max_retries: max number of retries
        is_tempered: True for tempered lock, False for non-tempered
    
    Returns:
        Response from boardctl.py or error code
        For check_door returns "1 1" for open door, "1 0" for closed door
    """
    try:
        if command not in ["enable_led", "disable_led"] and locker_id != "":
            try:
                locker_num = int(locker_id)
                if locker_num < 1 or locker_num > 206:
                    logger.error(f"Invalid locker ID {locker_id} - must be between 1 and 206")
                    return "-22"
            except ValueError:
                logger.error(f"Invalid locker ID format {locker_id} - must be a number")
                return "-22"
        else:
            locker_num = None  

        # Získáme port z konfigurace
        hardware_port = device_config.fsm_config["hardware_port"]
        
        if command == "unlock":
            # Pro temperovaný použijeme unlock_tempered, pro netemperovaný unlock
            action = "unlock_tempered" if is_tempered else "unlock"
            cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", action, str(locker_num)]
        elif command == "check_door":
            cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", "read_lock_state", str(locker_num)]
        elif command == "lock":
            # Lock se používá jen pro temperované zámky
            if not is_tempered:
                logger.warning(f"Lock command called for non-tempered lock {locker_id}")
                return "1"  # Vracíme úspěch, protože pro netemperované není potřeba zamykat
            cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", "lock_tempered"]
        elif command == "enable_led":
            cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", "enable_led"]
        elif command == "disable_led":
            cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", "disable_led"]
        elif command.startswith("write_led_colors"):
            # Format: write_led_colors:1:A:2:B:3:A (ledID:color:ledID:color:...)
            parts = command.split(":")
            if len(parts) < 3 or (len(parts) - 1) % 2 != 0:
                logger.error(f"Invalid write_led_colors format: {command}")
                return "-22"
            
            # Sestavíme příkaz: write_led_colors 1 A 2 B 3 A
            cmd_parts = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", "write_led_colors"]
            
            # Přidáme LED ID a barvy (přeskočíme první část "write_led_colors")
            for i in range(1, len(parts), 2):
                if i + 1 < len(parts):
                    led_id = parts[i]
                    color = parts[i + 1]
                    cmd_parts.extend([led_id, color])
            
            cmd = cmd_parts
        elif command == "read_temperature":
            # Read temperature from sensor - locker_id contains sensor_id
            cmd = [PYTHON_ALIAS, BOARDCTL_PATH, "-d", hardware_port, "-a", "read_temperature", str(locker_num)]
        else:
            logger.error(f"Unknown command: {command}")
            return "-22"

        # Spustíme příkaz s možností opakování
        for attempt in range(max_retries):
            logger.info(f"Executing command (attempt {attempt + 1}/{max_retries}): {' '.join(map(str, cmd))}")
            
            try:
                stdout_text, stderr_text, error_code = await execute_command(cmd, command)
                
                if error_code is None:  # Úspěch
                    if command == "check_door":
                        # Zpracování výstupu pro check_door
                        lines = stdout_text.split('\n')
                        if len(lines) >= 2:
                            try:
                                state = lines[-1].strip().split()
                                if len(state) == 2:
                                    # Vrátíme přímo formát "1 0" nebo "1 1"
                                    return f"{state[0]} {state[1]}"
                            except Exception as e:
                                logger.error(f"Failed to parse door state: {e}")
                        return "-22"  # Chyba parsování
                    elif command == "read_temperature":
                        # Zpracování výstupu pro read_temperature
                        lines = stdout_text.split('\n')
                        if len(lines) >= 2:
                            try:
                                # Hledáme poslední neprázdný řádek s daty
                                for line in reversed(lines):
                                    line = line.strip()
                                    if line and not line.startswith('Script'):
                                        # Vrátíme přímo výstup z boardctl.py
                                        return line
                            except Exception as e:
                                logger.error(f"Failed to parse temperature response: {e}")
                        return "-22"  # Chyba parsování
                    else:
                        # Pro ostatní příkazy vracíme úspěch
                        return "1"
                
                # Pokud není poslední pokus, počkáme před dalším pokusem
                if attempt < max_retries - 1:
                    logger.warning(f"Command failed with error code {error_code}, retrying in 2 seconds...")
                    await asyncio.sleep(2)
                else:
                    logger.error(f"Command failed after {max_retries} attempts with error code {error_code}")
                    return error_code
                    
            except Exception as e:
                logger.error(f"Error executing command (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                else:
                    return "-22"
            
    except Exception as e:
        logger.error(f"Error executing command {command}: {str(e)}")
        return "-22"