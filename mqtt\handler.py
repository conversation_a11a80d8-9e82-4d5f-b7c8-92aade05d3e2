import uuid
import asyncio
from typing import Dict, Any, List, Optional

class MQTTCommandHandler:
    """Handler for processing MQTT command messages"""

    def __init__(self):
        pass

    def handle_command(self, topic_parts: List[str], payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incoming MQTT command and return response

        Args:
            topic_parts: List of topic parts (split by '/')
            payload: Parsed JSON payload from the message

        Returns:
            Response dictionary to be published
        """
        try:
            command_type = topic_parts[3]  # electronic/system/sale/storage
            command_action = topic_parts[4] if len(topic_parts) > 4 else None

            # Validate request_uuid is present
            request_uuid = payload.get("request_uuid")
            if not request_uuid:
                response = {
                    "success": False,
                    "message": "Missing required field 'request_uuid'",
                    "request_uuid": str(uuid.uuid4())  # Generate one for the error response
                }
                return response

            # Default response
            response = {"success": True, "request_uuid": request_uuid}

            match command_type:
                # Handle ELECTRONIC commands
                case "electronic":
                    response = self.handle_electronic_command(command_action, payload, response)

                # Handle SYSTEM commands
                case "system":
                    response = self.handle_system_command(command_action, payload, response)

                # Handle SALE commands
                case "sale":
                    response = self.handle_sale_command(command_action, payload, response)

                # Handle storage commands
                case "storage":
                    response = self.handle_storage_command(command_action, payload, response)

            return response

        except Exception as e:
            # Return error response for any exceptions
            return {
                "success": False,
                "message": f"Error processing command: {str(e)}",
                "request_uuid": payload.get("request_uuid", str(uuid.uuid4()))
            }


    def handle_electronic_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle ELECTRONIC commands"""
        from managers.sequence_manager import sequence_manager
        from hardware.locker_control import LockerController
        from managers.session_manager import SessionType, SectionConfig
        import mysql.connector
        from os import getenv

        locker_controller = LockerController()
        section_id = payload.get("section_id", 1)
        response["section_id"] = section_id

        # Get section information from database
        section_info = self._get_section_info(section_id)
        if not section_info:
            response.update({
                "success": False,
                "message": f"Section {section_id} not found in database"
            })
            return response

        is_tempered = section_info["tempered"]

        match command_action:
            case "unlock":
                # Open section with unlock_locker() function
                try:
                    success = asyncio.run(locker_controller.unlock_locker(section_id))
                    if success:
                        response["message"] = f"Section {section_id} unlocked successfully"
                    else:
                        response.update({
                            "success": False,
                            "message": f"Failed to unlock section {section_id}"
                        })
                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error unlocking section {section_id}: {str(e)}"
                    })

            case "open_section":
                # Open section using _start_section_sequence from process_manager
                try:
                    from managers.process_manager import _start_section_sequence

                    # Create a session for the sequence
                    session_id = f"mqtt_open_{section_id}_{payload.get('request_uuid', 'unknown')[:8]}"

                    # Use _start_section_sequence which handles SectionConfig creation internally
                    sequence_success, successful_sections = asyncio.run(_start_section_sequence(
                        session_id=session_id,
                        section_ids=[section_id],
                        wait_for_completion=False  # Don't wait for completion in MQTT
                    ))

                    if sequence_success and successful_sections:
                        response["message"] = f"Section {section_id} opening sequence started successfully"
                        response["session_id"] = session_id
                    else:
                        response.update({
                            "success": False,
                            "message": f"Failed to start opening sequence for section {section_id}"
                        })
                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error starting opening sequence for section {section_id}: {str(e)}"
                    })

            case "door_state":
                # Check door state with check_door_state()
                try:
                    door_state = asyncio.run(locker_controller.check_door_state(section_id, is_tempered=is_tempered))
                    if door_state is True:
                        response["message"] = f"Door state checked successfully"
                        response["door_state"] = "open"
                    elif door_state is False:
                        response["message"] = f"Door state checked successfully"
                        response["door_state"] = "closed"
                    else:
                        response["message"] = f"Door state check failed for section {section_id}"
                        response["door_state"] = "unknown"
                        response["success"] = False
                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error checking door state for section {section_id}: {str(e)}"
                    })

            case _:
                response.update({
                    "success": False,
                    "message": f"Unknown electronic command: {command_action}",
                })

        return response

    def _get_section_info(self, section_id: int) -> Optional[Dict[str, Any]]:
        """Get section information from database"""
        try:
            import mysql.connector
            from os import getenv

            conn = mysql.connector.connect(
                host=getenv("DB_HOST"),
                port=int(getenv("DB_PORT", 3306)),
                database=getenv("DB_NAME"),
                user=getenv("DB_USER"),
                password=getenv("DB_PASSWORD"),
                autocommit=True
            )

            cursor = conn.cursor(dictionary=True)
            query = "SELECT lock_id, tempered FROM box_sections WHERE section_id = %s LIMIT 1"
            cursor.execute(query, (section_id,))

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            return result

        except mysql.connector.Error as err:
            print(f"Database error getting section info for {section_id}: {err}")
            return None
        except Exception as e:
            print(f"Exception getting section info for {section_id}: {e}")
            return None





    def handle_sale_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle SALE commands"""
        from infrastructure.repositories.product_repository import product_repository

        match command_action:
            case "edit_reservation":
                # Use update_sale_reservation_status to mark product as picked up
                try:
                    section_id = payload.get("section_id")
                    product_uuid = payload.get("product_uuid")

                    if not product_uuid and not section_id:
                        response.update({
                            "success": False,
                            "message": "Either 'product_uuid' or 'section_id' must be provided"
                        })
                        return response

                    # Try to update by product_uuid first, then by section_id
                    success = False
                    if product_uuid:
                        # Find product by UUID to get product_id
                        product_info = asyncio.run(product_repository.find_product_for_pickup(product_uuid=product_uuid))
                        if product_info:
                            success = product_repository.update_sale_reservation_status(
                                status=payload.get("status", 0),
                                product_id=product_info["id"]
                            )
                    elif section_id:
                        success = product_repository.update_sale_reservation_status(
                            status=payload.get("status", 0),
                            section_id=section_id
                        )

                    if success:
                        response["message"] = "Product reservation edited successfully"
                        response["status"] = payload.get("status", 0)
                        if product_uuid:
                            response["product_uuid"] = product_uuid
                        if section_id:
                            response["section_id"] = section_id
                    else:
                        response.update({
                            "success": False,
                            "message": "Failed to edit product reservation"
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error editing reservation: {str(e)}"
                    })

            case "reserve_product":
                # Use reserve_section to reserve a product
                try:
                    section_id = payload.get("section_id")
                    reservation_pin = payload.get("reservation_pin")
                    # Ensure reservation_pin is string if provided
                    if reservation_pin is not None:
                        reservation_pin = str(reservation_pin)

                    if not section_id:
                        response.update({
                            "success": False,
                            "message": "'section_id' must be provided"
                        })
                        return response

                    result = asyncio.run(product_repository.reserve_section(
                        section_id=section_id,
                        reservation_pin=reservation_pin
                    ))

                    if result:
                        response["message"] = "Product reserved successfully"
                        response["section_id"] = section_id
                        response["reservation_pin"] = str(result.get("reservation_pin")) if result.get("reservation_pin") else None
                        response["product_uuid"] = result.get("uuid")
                        response["price"] = result.get("price")
                    else:
                        response.update({
                            "success": False,
                            "message": f"Failed to reserve product in section {section_id}"
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error reserving product: {str(e)}"
                    })

            case "unreserve_product":
                # Use cancel_reservation to unreserve a product
                try:
                    section_id = payload.get("section_id")
                    reservation_pin = payload.get("reservation_pin")
                    product_uuid = payload.get("product_uuid")

                    # Ensure reservation_pin is string if provided
                    if reservation_pin is not None:
                        reservation_pin = str(reservation_pin)

                    if not section_id and not reservation_pin and not product_uuid:
                        response.update({
                            "success": False,
                            "message": "Either 'section_id', 'reservation_pin', or 'product_uuid' must be provided"
                        })
                        return response

                    # If we have reservation_pin or product_uuid, find the section_id
                    if not section_id:
                        if reservation_pin:
                            product_info = asyncio.run(product_repository.find_product_for_pickup(reservation_pin=reservation_pin))
                            if not product_info:
                                response.update({
                                    "success": False,
                                    "message": f"No active product found with reservation PIN '{reservation_pin}'"
                                })
                                return response
                            section_id = product_info.get("section_id")
                        elif product_uuid:
                            product_info = asyncio.run(product_repository.find_product_for_pickup(product_uuid=product_uuid))
                            if not product_info:
                                response.update({
                                    "success": False,
                                    "message": f"No product found with UUID '{product_uuid}'"
                                })
                                return response
                            section_id = product_info.get("section_id")
                            if not section_id:
                                response.update({
                                    "success": False,
                                    "message": f"Product with UUID '{product_uuid}' has no section_id"
                                })
                                return response

                    if not section_id:
                        response.update({
                            "success": False,
                            "message": "Could not determine section_id for unreservation"
                        })
                        return response

                    result = asyncio.run(product_repository.cancel_reservation(section_id))

                    if result:
                        response["message"] = "Product unreserved successfully"
                        response["section_id"] = section_id
                        response["cancelled_pin"] = str(result.get("cancelled_pin")) if result.get("cancelled_pin") else None
                        response["product_uuid"] = result.get("uuid")
                    else:
                        response.update({
                            "success": False,
                            "message": f"Failed to unreserve product in section {section_id}"
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error unreserving product: {str(e)}"
                    })

            case _:
                response.update({
                    "success": False,
                    "message": f"Unknown sale command: {command_action}",
                })

        return response





    def handle_storage_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle STORAGE commands"""
        from infrastructure.repositories.storage_repository import storage_repository

        match command_action:
            case "edit_reservation":
                # Use deactivate_reservation to mark storage reservation as completed
                try:
                    section_id = payload.get("section_id")
                    reservation_uuid = payload.get("reservation_uuid")
                    reservation_pin = payload.get("reservation_pin")

                    # Ensure reservation_pin is string if provided
                    if reservation_pin is not None:
                        reservation_pin = str(reservation_pin)

                    if not reservation_uuid and not section_id and not reservation_pin:
                        response.update({
                            "success": False,
                            "message": "Either 'reservation_uuid', 'section_id', or 'reservation_pin' must be provided"
                        })
                        return response

                    # Try different identification methods
                    success = False
                    if reservation_uuid:
                        # Find reservation by UUID to get reservation_id
                        reservation_info = storage_repository.find_reservation_by_pin(uuid=reservation_uuid)
                        if reservation_info:
                            success = storage_repository.deactivate_reservation(reservation_id=reservation_info["id"])
                    elif reservation_pin:
                        # Find reservation by PIN to get reservation_id
                        reservation_info = storage_repository.find_reservation_by_pin(reservation_pin)
                        if reservation_info:
                            success = storage_repository.deactivate_reservation(reservation_id=reservation_info["id"])
                    elif section_id:
                        success = storage_repository.deactivate_reservation(section_id=section_id)

                    if success:
                        response["message"] = "Storage reservation edited successfully"
                        response["status"] = payload.get("status", 0)
                        if reservation_uuid:
                            response["reservation_uuid"] = reservation_uuid
                        if section_id:
                            response["section_id"] = section_id
                        if reservation_pin:
                            response["reservation_pin"] = str(reservation_pin)
                    else:
                        response.update({
                            "success": False,
                            "message": "Failed to edit storage reservation"
                        })

                except Exception as e:
                    response.update({
                        "success": False,
                        "message": f"Error editing storage reservation: {str(e)}"
                    })

            case _:
                response.update({
                    "success": False,
                    "message": f"Unknown storage command: {command_action}",
                })

        return response





    def handle_system_command(self, command_action: str, payload: Dict[str, Any], response: Dict[str, Any]) -> Dict[str, Any]:
        """Handle SYSTEM commands"""
        match command_action:
            case "reboot_device":
                # TODO: Reboot device
                response["message"] = "Rebooting"
                return response
            case _:
                response.update({
                    "success": False,
                    "message": "Unknown command",
                })
                return response




# Global handler instance
command_handler = MQTTCommandHandler()
