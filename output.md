INFO:hardware.electronics_api:Executing command (attempt 1/2): py D:\GitLab\backend\hardware\boardctl.py -d COM7 -a enable_led
cmd: ['py', 'D:\\GitLab\\backend\\hardware\\boardctl.py', '-d', 'COM7', '-a', 'enable_led'], command_type: enable_led
anooooooo 1
anooooo 2
anooooo 5
anooooo 6







INFO:hardware.electronics_api:Executing command (attempt 1/2): py D:\GitLab\backend\hardware\boardctl.py -d COM7 -a enable_led
cmd: ['py', 'D:\\GitLab\\backend\\hardware\\boardctl.py', '-d', 'COM7', '-a', 'enable_led'], command_type: enable_led
anooooooo 1
anooooo 2
INFO:hardware.electronics_api:Command output - stdout:
Script finished successfuly
0
anooooo 3, error_code: None
INFO:hardware.locker_control:LED system enabled